import { browser } from "$app/environment";
import { goto } from "$app/navigation";
import { addToast } from "$lib/components/toast/toastStore";
import { userAuth, activationCode } from "$lib/stores/app.store";
import { get } from "svelte/store";
import { env } from "$env/dynamic/public";
import { InputValidator, authRateLimiter } from "$lib/utils/security";
import { createPersistedStore } from "./local.service";
import type { IAuthStrategy, AuthResponse, AuthUser, AuthError } from "./auth-strategy.types";
import { AuthStrategy } from "./auth-strategy.types";

// Firebase REST API 端点
const API_BASE_URL = "/api/googleapis?rest=";
const SIGN_UP_URL = "https://identitytoolkit.googleapis.com/v1/accounts:signUp";
const SIGN_IN_PASSWORD_URL = "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword";
const SIGN_IN_EMAIL_LINK_URL = "https://identitytoolkit.googleapis.com/v1/accounts:signInWithEmailLink";
const SEND_OOB_CODE_URL = "https://identitytoolkit.googleapis.com/v1/accounts:sendOobCode";
const UPDATE_ACCOUNT_URL = "https://identitytoolkit.googleapis.com/v1/accounts:update";
// const RESET_PASSWORD_URL = "https://identitytoolkit.googleapis.com/v1/accounts:resetPassword";
const REFRESH_TOKEN_URL = "https://securetoken.googleapis.com/v1/token";
const GET_ACCOUNT_INFO_URL = "https://identitytoolkit.googleapis.com/v1/accounts:lookup";

export class ServerProxyAuthStrategy implements IAuthStrategy {
  readonly strategyType = AuthStrategy.SERVER_PROXY;
  private refreshTimer: NodeJS.Timeout | null = null;
  private readonly TOKEN_REFRESH_INTERVAL = 59 * 60 * 1000; // 59 minutes
  private emailTemp = createPersistedStore<string>("emailTemp", "");

  constructor() {
    if (browser) {
      this.initializeAuth();
    }
  }

  private async initializeAuth() {
    if (this.isAuthenticated()) {
      await this.checkAndRefreshToken();
      const currentUser = get(userAuth);
      const userInfo = await this.getUserInfo();
      this._setUser({ ...currentUser, ...userInfo });
    }
  }

  async isAvailable(): Promise<boolean> {
    try {
      // 检查代理服务是否可用
      await fetch(`${API_BASE_URL}${GET_ACCOUNT_INFO_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ idToken: "test" }),
      });

      // 即使返回错误，只要能连接到代理服务就说明可用
      return true;
    } catch (error) {
      console.log('Server proxy not available:', error);
      return false;
    }
  }

  async getHealthStatus(): Promise<{ healthy: boolean; latency?: number }> {
    const startTime = Date.now();
    try {
      const available = await this.isAvailable();
      const latency = Date.now() - startTime;
      return { healthy: available, latency };
    } catch (error) {
      return { healthy: false };
    }
  }

  private _setUser(firebaseUserData: any): AuthUser {
    const user: AuthUser = {
      uid: firebaseUserData.localId || firebaseUserData.uid,
      email: firebaseUserData.email,
      emailVerified: firebaseUserData.emailVerified || false,
      idToken: firebaseUserData.idToken,
      refreshToken: firebaseUserData.refreshToken,
      expiresIn: firebaseUserData.expiresIn,
      localId: firebaseUserData.localId,
      registered: firebaseUserData.registered || false,
      ...firebaseUserData,
    };

    userAuth.set(user);
    return user;
  }

  async signUp(email: string, password: string): Promise<AuthResponse> {
    try {
      const emailValidation = InputValidator.validateEmail(email);
      if (!emailValidation.isValid) {
        return { success: false, error: { code: "INVALID_EMAIL", message: emailValidation.error || "邮箱格式无效" } };
      }

      const passwordValidation = InputValidator.validatePassword(password);
      if (!passwordValidation.isValid) {
        return { success: false, error: { code: "WEAK_PASSWORD", message: passwordValidation.errors[0] || "密码强度不够" } };
      }

      const rateLimitKey = `signup_${email.trim().toLowerCase()}`;
      if (!authRateLimiter.isAllowed(rateLimitKey)) {
        const remainingTime = Math.ceil(authRateLimiter.getRemainingTime(rateLimitKey) / 1000 / 60);
        return {
          success: false,
          error: {
            code: "TOO_MANY_ATTEMPTS",
            message: `注册尝试过于频繁，请${remainingTime}分钟后再试`
          }
        };
      }

      const cleanEmail = InputValidator.sanitizeString(email.trim(), 254);

      const response = await fetch(`${API_BASE_URL}${SIGN_UP_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: cleanEmail, password, returnSecureToken: true }),
      });

      const data = await response.json();
      if (!response.ok) return { success: false, error: this.parseFirebaseError(data.error) };

      const user = this._setUser(data);
      this.emailTemp.set(cleanEmail);
      await this.sendEmailVerification(user.idToken);

      authRateLimiter.reset(rateLimitKey);
      return this._checkVerificationAndActivation(data);
    } catch (error) {
      return this._handleNetworkError(error, "注册失败");
    }
  }

  async signIn(email: string, password: string): Promise<AuthResponse> {
    try {
      const emailValidation = InputValidator.validateEmail(email);
      if (!emailValidation.isValid) {
        return { success: false, error: { code: "INVALID_EMAIL", message: emailValidation.error || "邮箱格式无效" } };
      }

      if (!password || password.trim().length === 0) {
        return { success: false, error: { code: "MISSING_PASSWORD", message: "请输入密码" } };
      }

      const rateLimitKey = `signin_${email.trim().toLowerCase()}`;
      if (!authRateLimiter.isAllowed(rateLimitKey)) {
        const remainingTime = Math.ceil(authRateLimiter.getRemainingTime(rateLimitKey) / 1000 / 60);
        return {
          success: false,
          error: {
            code: "TOO_MANY_ATTEMPTS",
            message: `登录尝试过于频繁，请${remainingTime}分钟后再试`
          }
        };
      }

      const cleanEmail = InputValidator.sanitizeString(email.trim(), 254);

      const response = await fetch(`${API_BASE_URL}${SIGN_IN_PASSWORD_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: cleanEmail, password, returnSecureToken: true }),
      });

      const data = await response.json();
      if (!response.ok) {
        return { success: false, error: this.parseFirebaseError(data.error) };
      }

      authRateLimiter.reset(rateLimitKey);

      const userInfo = await this.getUserInfo(data.idToken);
      const user = this._setUser({ ...data, ...userInfo });
      return this._checkVerificationAndActivation(user);

    } catch (error) {
      return this._handleNetworkError(error, "登录失败");
    }
  }

  async signInWithGoogle(): Promise<AuthResponse> {
    // 服务端代理策略不支持Google登录，需要切换到Firebase SDK策略
    return {
      success: false,
      error: {
        code: "UNSUPPORTED_OPERATION",
        message: "服务端代理模式不支持Google登录，请切换到直连模式"
      }
    };
  }

  async signOut(): Promise<void> {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
    
    userAuth.set({} as AuthUser);
    activationCode.set("");
    this.emailTemp.set("");
    
    if (browser) {
      goto("/login");
    }
  }

  async sendEmailVerification(idToken: string): Promise<boolean> {
    try {
      const response = await this.sendOobcode("VERIFY_EMAIL", { idToken });
      return response.ok;
    } catch (error) {
      console.error("发送验证邮件失败:", error);
      return false;
    }
  }

  async sendPasswordResetEmail(email: string): Promise<boolean> {
    try {
      const response = await this.sendOobcode("PASSWORD_RESET", { email });
      return response.ok;
    } catch (error) {
      console.error("发送重置密码邮件失败:", error);
      return false;
    }
  }

  async sendEmailLinkSignIn(email: string): Promise<boolean> {
    try {
      const response = await this.sendOobcode("EMAIL_SIGNIN", { email });
      return response.ok;
    } catch (error) {
      console.error("发送邮箱链接登录邮件失败:", error);
      return false;
    }
  }

  async sendOobcode(requestType: "EMAIL_SIGNIN" | "PASSWORD_RESET" | "VERIFY_EMAIL" | "VERIFY_AND_CHANGE_EMAIL", data: any) {
    const continueUrl = `${window.location.origin}${env.PUBLIC_GOOGLE_FIREBASE_AUTH_CONTINUE_URL ?? ""}`
    return fetch(`${API_BASE_URL}${SEND_OOB_CODE_URL}`, {
      method: "POST",
      headers: { "Content-Type": "application/json", "X-Firebase-Locale": navigator.language || "en" },
      body: JSON.stringify({ requestType, continueUrl, ...data }),
    });
  }

  async handleAuthCallback(mode: string, oobCode: string): Promise<AuthResponse> {
    switch (mode) {
      case "signIn":
        return await this.handleEmailLinkSignIn(oobCode);
      case "verifyEmail":
        return await this.handleEmailVerification(oobCode);
      case "resetPassword":
        return { success: true, redirectUrl: `/auth/reset-password?oobCode=${encodeURIComponent(oobCode)}` };
      default:
        throw new Error("无效的操作链接");
    }
  }

  private async handleEmailLinkSignIn(oobCode: string): Promise<AuthResponse> {
    try {
      const email = get(this.emailTemp);
      if (!email) throw new Error("邮箱信息丢失，请重新发送登录链接");

      const response = await fetch(`${API_BASE_URL}${SIGN_IN_EMAIL_LINK_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, oobCode }),
      });

      const data = await response.json();
      if (!response.ok) throw data.error;

      const userInfo = await this.getUserInfo(data.idToken);
      this.emailTemp.set("");
      const user = this._setUser({ ...data, ...userInfo });

      addToast({ type: "success", message: `邮件链接登录成功！欢迎 ${user.email}` });
      return this._checkVerificationAndActivation(user);
    } catch (error) {
      const errorMessage = this.parseFirebaseError(error).message;
      addToast({ type: "error", message: errorMessage });
      throw new Error("邮件链接登录失败: " + errorMessage);
    }
  }

  private async handleEmailVerification(oobCode: string): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}${UPDATE_ACCOUNT_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ oobCode }),
      });

      const data = await response.json();
      if (!response.ok) throw this.parseFirebaseError(data);

      const currentUser = get(userAuth);
      if (currentUser.idToken) {
        const user = this._setUser({ ...currentUser, ...data })
        addToast({ type: "success", message: "邮箱验证成功！" });
        return this._checkVerificationAndActivation(user);
      } else {
        addToast({ type: "success", message: "邮箱验证成功！请重新登录" });
        return { success: true };
      }
    } catch (error) {
      addToast({ type: "error", message: "邮箱验证失败，请重试" });
      throw this._handleNetworkError(error, "邮箱验证失败, 请重试")
    }
  }

  async refreshToken(): Promise<boolean> {
    try {
      const currentUser = get(userAuth);
      if (!currentUser?.refreshToken) return false;

      const response = await fetch(`${API_BASE_URL}${REFRESH_TOKEN_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          grant_type: "refresh_token",
          refresh_token: currentUser.refreshToken,
        }),
      });

      const data = await response.json();
      if (!response.ok) {
        this.signOut();
        return false;
      }

      this._setUser({
        ...currentUser,
        idToken: data.id_token,
        refreshToken: data.refresh_token,
        expiresIn: data.expires_in,
      });

      this.scheduleTokenRefresh();
      return true;
    } catch (error) {
      console.error("Token refresh failed:", error);
      this.signOut();
      return false;
    }
  }

  private scheduleTokenRefresh() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    this.refreshTimer = setTimeout(() => {
      this.refreshToken();
    }, this.TOKEN_REFRESH_INTERVAL);
  }

  private async checkAndRefreshToken() {
    const currentUser = get(userAuth);
    if (!currentUser?.idToken) return;

    try {
      const payload = JSON.parse(atob(currentUser.idToken.split('.')[1]));
      const expirationTime = payload.exp * 1000;
      const currentTime = Date.now();
      const timeUntilExpiry = expirationTime - currentTime;

      if (timeUntilExpiry < 5 * 60 * 1000) {
        await this.refreshToken();
      } else {
        this.scheduleTokenRefresh();
      }
    } catch (error) {
      console.error("Token validation failed:", error);
      await this.refreshToken();
    }
  }

  getCurrentUser(): AuthUser | null {
    const user = get(userAuth);
    return user && user.idToken ? user : null;
  }

  async getUserInfo(idToken?: string): Promise<any> {
    const token = idToken || this.getCurrentUser()?.idToken;
    if (!token) {
      console.warn("用户未登录");
      return false;
    }

    try {
      const response = await fetch(`${API_BASE_URL}${GET_ACCOUNT_INFO_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ idToken: token }),
      });

      const data = await response.json();
      if (!response.ok) {
        this.signOut();
        return false;
      }

      if (!data.users || 0 == data.users.length) {
        this.signOut();
        return false;
      }

      return data.users[0];
    } catch (error) {
      this.signOut();
      throw error;
    }
  }

  isAuthenticated(): boolean {
    return !!this.getCurrentUser()?.idToken;
  }

  isEmailVerified(): boolean {
    return !!this.getCurrentUser()?.emailVerified;
  }

  isActivated(): boolean {
    return !!this.getCurrentUser()?.activated;
  }

  async activateUser(code: string): Promise<boolean> {
    try {
      const currentUser = this.getCurrentUser();
      if (!currentUser) {
        addToast({ type: "error", message: "请先登录" });
        return false;
      }

      const rateLimitKey = `activate_${currentUser.email}`;
      if (!authRateLimiter.isAllowed(rateLimitKey)) {
        const remainingTime = Math.ceil(authRateLimiter.getRemainingTime(rateLimitKey) / 1000 / 60);
        addToast({ type: "error", message: `激活尝试过于频繁，请${remainingTime}分钟后再试` });
        return false;
      }

      const cleanCode = InputValidator.sanitizeString(code.replace(/[^A-Za-z0-9]/g, '').toUpperCase(), 20);

      const response = await fetch("/api/user/activate", {
        method: "POST",
        headers: { "Content-Type": "application/json", "Authorization": `Bearer ${currentUser.idToken}` },
        body: JSON.stringify({ email: currentUser.email, activationCode: cleanCode }),
      });

      if (response.ok) {
        activationCode.set(cleanCode);
        authRateLimiter.reset(rateLimitKey);
        addToast({ type: "success", message: "激活成功！欢迎使用蘑菇AI小说平台" });
        return true;
      }
      const data = await response.json();
      throw new Error(data.error || "激活码验证失败");
    } catch (error: any) {
      addToast({ type: "error", message: error.message || "激活码验证失败" });
      return false;
    }
  }

  private _checkVerificationAndActivation(user: AuthUser): AuthResponse {
    if (!user.emailVerified) {
      return { success: true, user, redirectUrl: "/auth/verify-email" };
    }
    if (!user.activated) {
      return { success: true, user, redirectUrl: "/auth/activate" };
    }
    return { success: true, user };
  }

  private parseFirebaseError(error: any): AuthError {
    const errorCode = error?.error?.message || error?.message || "UNKNOWN_ERROR";

    const errorMessages: { [key: string]: string } = {
      "EMAIL_EXISTS": "该邮箱已被注册",
      "OPERATION_NOT_ALLOWED": "该操作不被允许",
      "TOO_MANY_ATTEMPTS_TRY_LATER": "尝试次数过多，请稍后再试",
      "EMAIL_NOT_FOUND": "邮箱不存在",
      "INVALID_PASSWORD": "密码错误",
      "USER_DISABLED": "用户账号已被禁用",
      "INVALID_EMAIL": "邮箱格式无效",
      "WEAK_PASSWORD": "密码强度不够",
      "MISSING_PASSWORD": "请输入密码",
      "INVALID_ID_TOKEN": "登录状态已过期，请重新登录",
      "USER_NOT_FOUND": "用户不存在",
      "CREDENTIAL_TOO_OLD_LOGIN_AGAIN": "登录状态已过期，请重新登录",
    };

    return {
      code: errorCode,
      message: errorMessages[errorCode] || `认证失败: ${errorCode}`
    };
  }

  private _handleNetworkError(error: any, defaultMessage: string): AuthResponse {
    console.error(defaultMessage, error);

    if (error.name === 'AbortError') {
      return { success: false, error: { code: "REQUEST_TIMEOUT", message: "请求超时，请检查网络连接" } };
    }

    if (!navigator.onLine) {
      return { success: false, error: { code: "NETWORK_ERROR", message: "网络连接失败，请检查网络设置" } };
    }

    return { success: false, error: { code: "NETWORK_ERROR", message: defaultMessage } };
  }
}
