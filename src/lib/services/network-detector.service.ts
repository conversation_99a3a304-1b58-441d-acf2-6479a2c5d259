import { browser } from "$app/environment";
import type { INetworkDetector, NetworkStatus } from "./auth-strategy.types";
import { createPersistedStore } from "./local.service";

export class NetworkDetectorService implements INetworkDetector {
  private monitoringInterval: NodeJS.Timeout | null = null;
  private monitoringCallback: ((status: NetworkStatus) => void) | null = null;
  private lastStatus: NetworkStatus | null = null;
  
  // 缓存网络状态，避免频繁检测
  private networkStatusStore = createPersistedStore<NetworkStatus>("networkStatus", {
    isOnline: true,
    canAccessFirebase: false,
    canAccessGoogle: false,
    lastChecked: 0
  });

  constructor(
    private checkInterval: number = 30000, // 30秒检测一次
    private timeout: number = 5000 // 5秒超时
  ) {}

  async checkNetworkStatus(): Promise<NetworkStatus> {
    if (!browser) {
      return {
        isOnline: false,
        canAccessFirebase: false,
        canAccessGoogle: false,
        lastChecked: Date.now()
      };
    }

    const now = Date.now();
    
    // 如果最近检测过且时间间隔不长，返回缓存结果
    if (this.lastStatus && (now - this.lastStatus.lastChecked) < 10000) {
      return this.lastStatus;
    }

    const isOnline = navigator.onLine;
    
    if (!isOnline) {
      const status: NetworkStatus = {
        isOnline: false,
        canAccessFirebase: false,
        canAccessGoogle: false,
        lastChecked: now
      };
      this.updateStatus(status);
      return status;
    }

    // 并行检测Firebase和Google服务的可访问性
    const [canAccessFirebase, canAccessGoogle] = await Promise.all([
      this.checkFirebaseAccess(),
      this.checkGoogleAccess()
    ]);

    const status: NetworkStatus = {
      isOnline,
      canAccessFirebase,
      canAccessGoogle,
      lastChecked: now
    };

    this.updateStatus(status);
    return status;
  }

  async checkFirebaseAccess(): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      // 尝试访问Firebase的公开端点
      const response = await fetch('https://firebase.googleapis.com/v1beta1/projects', {
        method: 'GET',
        signal: controller.signal,
        mode: 'no-cors' // 避免CORS问题
      });

      clearTimeout(timeoutId);
      
      // 对于no-cors模式，只要没有抛出错误就说明可以访问
      return true;
    } catch (error) {
      console.log('Firebase access check failed:', error);
      return false;
    }
  }

  async checkGoogleAccess(): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      // 尝试访问Google的公开端点
      const response = await fetch('https://www.googleapis.com/oauth2/v1/tokeninfo', {
        method: 'GET',
        signal: controller.signal,
        mode: 'no-cors'
      });

      clearTimeout(timeoutId);
      return true;
    } catch (error) {
      console.log('Google access check failed:', error);
      return false;
    }
  }

  startMonitoring(callback: (status: NetworkStatus) => void): void {
    this.monitoringCallback = callback;
    
    // 立即执行一次检测
    this.checkNetworkStatus().then(status => {
      callback(status);
    });

    // 设置定期检测
    this.monitoringInterval = setInterval(async () => {
      const status = await this.checkNetworkStatus();
      callback(status);
    }, this.checkInterval);

    // 监听网络状态变化事件
    if (browser) {
      window.addEventListener('online', this.handleOnlineStatusChange);
      window.addEventListener('offline', this.handleOnlineStatusChange);
    }
  }

  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    this.monitoringCallback = null;

    if (browser) {
      window.removeEventListener('online', this.handleOnlineStatusChange);
      window.removeEventListener('offline', this.handleOnlineStatusChange);
    }
  }

  private handleOnlineStatusChange = async () => {
    if (this.monitoringCallback) {
      const status = await this.checkNetworkStatus();
      this.monitoringCallback(status);
    }
  };

  private updateStatus(status: NetworkStatus): void {
    this.lastStatus = status;
    this.networkStatusStore.set(status);
  }

  // 获取缓存的网络状态
  getCachedStatus(): NetworkStatus | null {
    return this.lastStatus;
  }

  // 强制刷新网络状态
  async forceRefresh(): Promise<NetworkStatus> {
    this.lastStatus = null;
    return await this.checkNetworkStatus();
  }
}

// 单例实例
export const networkDetector = new NetworkDetectorService();
