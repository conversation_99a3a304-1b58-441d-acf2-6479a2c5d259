import { browser } from "$app/environment";
import { authStrategyFactory } from "./auth-strategy-factory.service";
import type { IAuthStrategy, AuthResponse, AuthUser, AuthStrategyConfig } from "./auth-strategy.types";
import { AuthStrategy } from "./auth-strategy.types";

/**
 * 统一认证服务
 * 这个服务作为认证策略的统一入口，自动选择最佳的认证策略
 */
export class UnifiedAuthService {
  private factory = authStrategyFactory;

  constructor() {
    if (browser) {
      this.initialize();
    }
  }

  private async initialize() {
    // 工厂会自动初始化和选择策略
    console.log('UnifiedAuthService initialized');
  }

  // 获取当前使用的认证策略
  private getCurrentStrategy(): IAuthStrategy {
    return this.factory.getCurrentStrategy();
  }

  // --- 基础认证方法 ---

  async signUp(email: string, password: string): Promise<AuthResponse> {
    try {
      return await this.getCurrentStrategy().signUp(email, password);
    } catch (error) {
      console.error('SignUp failed:', error);
      return { success: false, error: { code: "STRATEGY_ERROR", message: "注册失败，请重试" } };
    }
  }

  async signIn(email: string, password: string): Promise<AuthResponse> {
    try {
      return await this.getCurrentStrategy().signIn(email, password);
    } catch (error) {
      console.error('SignIn failed:', error);
      return { success: false, error: { code: "STRATEGY_ERROR", message: "登录失败，请重试" } };
    }
  }

  async signInWithGoogle(): Promise<AuthResponse> {
    try {
      const strategy = this.getCurrentStrategy();
      
      // 如果当前策略不支持Google登录，尝试切换到Firebase SDK
      if (strategy.strategyType === AuthStrategy.SERVER_PROXY) {
        try {
          await this.factory.switchStrategy(AuthStrategy.FIREBASE_SDK);
          return await this.getCurrentStrategy().signInWithGoogle();
        } catch (switchError) {
          console.error('Failed to switch to Firebase SDK for Google login:', switchError);
          return { 
            success: false, 
            error: { 
              code: "GOOGLE_LOGIN_UNAVAILABLE", 
              message: "Google登录暂时不可用，请使用邮箱密码登录" 
            } 
          };
        }
      }
      
      return await strategy.signInWithGoogle();
    } catch (error) {
      console.error('Google SignIn failed:', error);
      return { success: false, error: { code: "STRATEGY_ERROR", message: "Google登录失败，请重试" } };
    }
  }

  async signOut(redirect: boolean = true): Promise<void> {
    try {
      await this.getCurrentStrategy().signOut();
    } catch (error) {
      console.error('SignOut failed:', error);
    }
  }

  // --- 邮箱相关 ---

  async sendEmailVerification(idToken: string): Promise<boolean> {
    try {
      return await this.getCurrentStrategy().sendEmailVerification(idToken);
    } catch (error) {
      console.error('Send email verification failed:', error);
      return false;
    }
  }

  async sendPasswordResetEmail(email: string): Promise<boolean> {
    try {
      return await this.getCurrentStrategy().sendPasswordResetEmail(email);
    } catch (error) {
      console.error('Send password reset email failed:', error);
      return false;
    }
  }

  async sendEmailLinkSignIn(email: string): Promise<boolean> {
    try {
      const strategy = this.getCurrentStrategy();
      
      // 邮箱链接登录主要通过服务端代理实现
      if (strategy.strategyType === AuthStrategy.FIREBASE_SDK) {
        try {
          await this.factory.switchStrategy(AuthStrategy.SERVER_PROXY);
          return await this.getCurrentStrategy().sendEmailLinkSignIn(email);
        } catch (switchError) {
          console.error('Failed to switch to server proxy for email link:', switchError);
          return false;
        }
      }
      
      return await strategy.sendEmailLinkSignIn(email);
    } catch (error) {
      console.error('Send email link failed:', error);
      return false;
    }
  }

  // --- OOB 处理 ---

  async handleAuthCallback(mode: string, oobCode: string): Promise<AuthResponse> {
    try {
      return await this.getCurrentStrategy().handleAuthCallback(mode, oobCode);
    } catch (error) {
      console.error('Handle auth callback failed:', error);
      return { success: false, error: { code: "CALLBACK_ERROR", message: "处理认证回调失败" } };
    }
  }

  // --- Token 管理 ---

  async refreshToken(): Promise<boolean> {
    try {
      return await this.getCurrentStrategy().refreshToken();
    } catch (error) {
      console.error('Refresh token failed:', error);
      return false;
    }
  }

  getCurrentUser(): AuthUser | null {
    try {
      return this.getCurrentStrategy().getCurrentUser();
    } catch (error) {
      console.error('Get current user failed:', error);
      return null;
    }
  }

  async getUserInfo(idToken?: string): Promise<any> {
    try {
      return await this.getCurrentStrategy().getUserInfo(idToken);
    } catch (error) {
      console.error('Get user info failed:', error);
      return false;
    }
  }

  // --- 状态检查 ---

  isAuthenticated(): boolean {
    try {
      return this.getCurrentStrategy().isAuthenticated();
    } catch (error) {
      console.error('Check authentication failed:', error);
      return false;
    }
  }

  isEmailVerified(): boolean {
    try {
      return this.getCurrentStrategy().isEmailVerified();
    } catch (error) {
      console.error('Check email verification failed:', error);
      return false;
    }
  }

  isActivated(): boolean {
    try {
      return this.getCurrentStrategy().isActivated();
    } catch (error) {
      console.error('Check activation failed:', error);
      return false;
    }
  }

  // --- 激活相关 ---

  async activateUser(code: string): Promise<boolean> {
    try {
      return await this.getCurrentStrategy().activateUser(code);
    } catch (error) {
      console.error('Activate user failed:', error);
      return false;
    }
  }

  // --- 策略管理 ---

  getCurrentStrategyType(): AuthStrategy {
    return this.factory.getCurrentStrategyType();
  }

  async switchStrategy(strategy: AuthStrategy): Promise<void> {
    await this.factory.switchStrategy(strategy);
  }

  async getStrategiesHealth() {
    return await this.factory.getStrategiesHealth();
  }

  updateConfig(config: Partial<AuthStrategyConfig>) {
    this.factory.updateConfig(config);
  }

  getConfig(): AuthStrategyConfig {
    return this.factory.getConfig();
  }

  async refreshAndRecommend(): Promise<AuthStrategy> {
    return await this.factory.refreshAndRecommend();
  }

  // --- 兼容性方法 ---
  // 这些方法保持与原有AuthService的兼容性

  async checkAndRefreshToken(): Promise<void> {
    await this.refreshToken();
  }

  // 兼容旧的方法名
  async verifyActivationCode(code: string): Promise<boolean> {
    return await this.activateUser(code);
  }

  // 清理资源
  destroy() {
    this.factory.destroy();
  }
}

// 单例实例
export const unifiedAuthService = new UnifiedAuthService();

// 为了向后兼容，也导出为authService
export const authService = unifiedAuthService;
