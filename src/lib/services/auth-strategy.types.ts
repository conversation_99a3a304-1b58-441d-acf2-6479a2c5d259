// 认证策略类型定义

export interface AuthUser {
  uid: string;
  email: string;
  displayName?: string;
  emailVerified: boolean;
  photoURL?: string;
  phoneNumber?: string;
  createdAt?: string;
  lastLoginAt?: string;
  isAnonymous?: boolean;
  idToken: string;
  refreshToken: string;
  expiresIn?: string;
  localId?: string;
  registered?: boolean;
  activated?: boolean;
  providerData?: any[];
  [key: string]: any;
}

export interface AuthError {
  code: string;
  message: string;
}

export interface AuthResponse {
  success: boolean;
  user?: AuthUser;
  error?: AuthError;
  redirectUrl?: string;
}

export interface NetworkStatus {
  isOnline: boolean;
  canAccessFirebase: boolean;
  canAccessGoogle: boolean;
  latency?: number;
  lastChecked: number;
}

export const AuthStrategy = {
  FIREBASE_SDK: 'firebase_sdk' as const,
  SERVER_PROXY: 'server_proxy' as const,
  HYBRID: 'hybrid' as const
} as const;

export type AuthStrategy = typeof AuthStrategy[keyof typeof AuthStrategy];

export interface AuthStrategyConfig {
  strategy: AuthStrategy;
  fallbackStrategy?: AuthStrategy;
  autoSwitch: boolean;
  checkInterval: number; // 网络检测间隔（毫秒）
  timeout: number; // 网络检测超时（毫秒）
}

// 认证策略接口
export interface IAuthStrategy {
  readonly strategyType: AuthStrategy;
  
  // 基础认证方法
  signUp(email: string, password: string): Promise<AuthResponse>;
  signIn(email: string, password: string): Promise<AuthResponse>;
  signInWithGoogle(): Promise<AuthResponse>;
  signOut(): Promise<void>;
  
  // 邮箱相关
  sendEmailVerification(idToken: string): Promise<boolean>;
  sendPasswordResetEmail(email: string): Promise<boolean>;
  sendEmailLinkSignIn(email: string): Promise<boolean>;
  
  // OOB 处理
  handleAuthCallback(mode: string, oobCode: string): Promise<AuthResponse>;
  
  // Token 管理
  refreshToken(): Promise<boolean>;
  getCurrentUser(): AuthUser | null;
  getUserInfo(idToken?: string): Promise<any>;
  
  // 状态检查
  isAuthenticated(): boolean;
  isEmailVerified(): boolean;
  isActivated(): boolean;
  
  // 激活相关
  activateUser(code: string): Promise<boolean>;
  
  // 策略特定方法
  isAvailable(): Promise<boolean>;
  getHealthStatus(): Promise<{ healthy: boolean; latency?: number }>;
}

// 网络检测服务接口
export interface INetworkDetector {
  checkNetworkStatus(): Promise<NetworkStatus>;
  checkFirebaseAccess(): Promise<boolean>;
  checkGoogleAccess(): Promise<boolean>;
  startMonitoring(callback: (status: NetworkStatus) => void): void;
  stopMonitoring(): void;
  forceRefresh(): Promise<NetworkStatus>;
}

// 认证策略工厂接口
export interface IAuthStrategyFactory {
  createStrategy(strategy: AuthStrategy): IAuthStrategy;
  getRecommendedStrategy(networkStatus: NetworkStatus): AuthStrategy;
  switchStrategy(newStrategy: AuthStrategy): Promise<void>;
}
