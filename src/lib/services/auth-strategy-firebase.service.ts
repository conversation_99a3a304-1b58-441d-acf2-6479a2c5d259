import { browser } from "$app/environment";
import { goto } from "$app/navigation";
import { addToast } from "$lib/components/toast/toastStore";
import { userAuth, activationCode } from "$lib/stores/app.store";
import { get } from "svelte/store";
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  sendEmailVerification as firebaseSendEmailVerification,
  sendPasswordResetEmail as firebaseSendPasswordResetEmail,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  getIdToken
} from "firebase/auth";
import { fireapp } from "$lib/firebase/firebase";
import { InputValidator, authRateLimiter } from "$lib/utils/security";
import type { IAuthStrategy, AuthResponse, AuthUser, AuthError } from "./auth-strategy.types";
import { AuthStrategy } from "./auth-strategy.types";

export class FirebaseSDKAuthStrategy implements IAuthStrategy {
  readonly strategyType = AuthStrategy.FIREBASE_SDK;
  private refreshTimer: NodeJS.Timeout | null = null;
  private readonly TOKEN_REFRESH_INTERVAL = 59 * 60 * 1000; // 59 minutes
  private unsubscribeAuth: (() => void) | null = null;

  constructor() {
    if (browser) {
      this.initializeAuth();
    }
  }

  private async initializeAuth() {
    try {
      const { auth } = fireapp();
      
      // 监听认证状态变化
      this.unsubscribeAuth = onAuthStateChanged(auth, async (firebaseUser) => {
        if (firebaseUser) {
          const idToken = await getIdToken(firebaseUser);
          this._setUser({
            ...firebaseUser,
            idToken,
            localId: firebaseUser.uid,
            registered: true
          });
          this.scheduleTokenRefresh();
        } else {
          userAuth.set({} as AuthUser);
        }
      });
    } catch (error) {
      console.error("Firebase SDK initialization failed:", error);
    }
  }

  async isAvailable(): Promise<boolean> {
    try {
      const { auth } = fireapp();
      // 尝试获取当前用户状态来测试Firebase连接
      return auth !== null;
    } catch (error) {
      console.log('Firebase SDK not available:', error);
      return false;
    }
  }

  async getHealthStatus(): Promise<{ healthy: boolean; latency?: number }> {
    const startTime = Date.now();
    try {
      const available = await this.isAvailable();
      const latency = Date.now() - startTime;
      
      if (available) {
        // 额外检查网络连接
        try {
          const { auth } = fireapp();
          await getIdToken(auth.currentUser!, true); // 强制刷新token来测试连接
          return { healthy: true, latency };
        } catch (error) {
          // 如果没有当前用户或网络问题，仍然认为SDK可用但可能有网络问题
          return { healthy: true, latency };
        }
      }
      
      return { healthy: false };
    } catch (error) {
      return { healthy: false };
    }
  }

  private _setUser(firebaseUserData: any): AuthUser {
    const user: AuthUser = {
      uid: firebaseUserData.uid,
      email: firebaseUserData.email,
      emailVerified: firebaseUserData.emailVerified || false,
      idToken: firebaseUserData.idToken,
      refreshToken: firebaseUserData.refreshToken || '',
      expiresIn: '3600', // Firebase默认1小时
      localId: firebaseUserData.localId || firebaseUserData.uid,
      registered: firebaseUserData.registered || true,
      ...firebaseUserData,
    };

    userAuth.set(user);
    return user;
  }

  async signUp(email: string, password: string): Promise<AuthResponse> {
    try {
      const emailValidation = InputValidator.validateEmail(email);
      if (!emailValidation.isValid) {
        return { success: false, error: { code: "INVALID_EMAIL", message: emailValidation.error || "邮箱格式无效" } };
      }

      if (!password || password.trim().length < 6) {
        return { success: false, error: { code: "WEAK_PASSWORD", message: "密码至少需要6个字符" } };
      }

      const rateLimitKey = `signup_${email.trim().toLowerCase()}`;
      if (!authRateLimiter.isAllowed(rateLimitKey)) {
        const remainingTime = Math.ceil(authRateLimiter.getRemainingTime(rateLimitKey) / 1000 / 60);
        return {
          success: false,
          error: {
            code: "TOO_MANY_ATTEMPTS",
            message: `注册尝试过于频繁，请${remainingTime}分钟后再试`
          }
        };
      }

      const cleanEmail = InputValidator.sanitizeString(email.trim(), 254);
      const { auth } = fireapp();

      const userCredential = await createUserWithEmailAndPassword(auth, cleanEmail, password);
      const idToken = await getIdToken(userCredential.user);
      
      const user = this._setUser({
        ...userCredential.user,
        idToken,
        localId: userCredential.user.uid,
        registered: true
      });

      // 发送验证邮件
      await this.sendEmailVerification(idToken);
      
      authRateLimiter.reset(rateLimitKey);
      return this._checkVerificationAndActivation(user);
    } catch (error: any) {
      return { success: false, error: this.parseFirebaseError(error) };
    }
  }

  async signIn(email: string, password: string): Promise<AuthResponse> {
    try {
      const emailValidation = InputValidator.validateEmail(email);
      if (!emailValidation.isValid) {
        return { success: false, error: { code: "INVALID_EMAIL", message: emailValidation.error || "邮箱格式无效" } };
      }

      if (!password || password.trim().length === 0) {
        return { success: false, error: { code: "MISSING_PASSWORD", message: "请输入密码" } };
      }

      const rateLimitKey = `signin_${email.trim().toLowerCase()}`;
      if (!authRateLimiter.isAllowed(rateLimitKey)) {
        const remainingTime = Math.ceil(authRateLimiter.getRemainingTime(rateLimitKey) / 1000 / 60);
        return {
          success: false,
          error: {
            code: "TOO_MANY_ATTEMPTS",
            message: `登录尝试过于频繁，请${remainingTime}分钟后再试`
          }
        };
      }

      const cleanEmail = InputValidator.sanitizeString(email.trim(), 254);
      const { auth } = fireapp();

      const userCredential = await signInWithEmailAndPassword(auth, cleanEmail, password);
      const idToken = await getIdToken(userCredential.user);
      
      const user = this._setUser({
        ...userCredential.user,
        idToken,
        localId: userCredential.user.uid,
        registered: true
      });

      authRateLimiter.reset(rateLimitKey);
      return this._checkVerificationAndActivation(user);

    } catch (error: any) {
      return { success: false, error: this.parseFirebaseError(error) };
    }
  }

  async signInWithGoogle(): Promise<AuthResponse> {
    try {
      const { auth } = fireapp();
      const provider = new GoogleAuthProvider();
      provider.addScope("profile");
      provider.addScope("email");
      provider.setCustomParameters({ prompt: "select_account" });

      const result = await signInWithPopup(auth, provider);
      const idToken = await getIdToken(result.user);

      const user = this._setUser({
        ...result.user,
        idToken,
        localId: result.user.uid,
        registered: true
      });

      addToast({ type: "success", message: "Google 登录成功" });
      return this._checkVerificationAndActivation(user);

    } catch (error: any) {
      addToast({ type: "error", message: error.message || "Google 登录失败" });
      return { success: false, error: this.parseFirebaseError(error) };
    }
  }

  async signOut(): Promise<void> {
    try {
      const { auth } = fireapp();
      await firebaseSignOut(auth);
      
      if (this.refreshTimer) {
        clearTimeout(this.refreshTimer);
        this.refreshTimer = null;
      }
      
      userAuth.set({} as AuthUser);
      activationCode.set("");
      
      if (browser) {
        goto("/login");
      }
    } catch (error) {
      console.error("Sign out failed:", error);
    }
  }

  async sendEmailVerification(_idToken: string): Promise<boolean> {
    try {
      const { auth } = fireapp();
      if (auth.currentUser) {
        await firebaseSendEmailVerification(auth.currentUser);
        return true;
      }
      return false;
    } catch (error) {
      console.error("发送验证邮件失败:", error);
      return false;
    }
  }

  async sendPasswordResetEmail(email: string): Promise<boolean> {
    try {
      const { auth } = fireapp();
      await firebaseSendPasswordResetEmail(auth, email);
      return true;
    } catch (error) {
      console.error("发送重置密码邮件失败:", error);
      return false;
    }
  }

  async sendEmailLinkSignIn(_email: string): Promise<boolean> {
    // Firebase SDK的邮箱链接登录需要特殊配置，暂时不支持
    addToast({ type: "error", message: "邮箱链接登录暂不支持，请使用密码登录" });
    return false;
  }

  async handleAuthCallback(mode: string, oobCode: string): Promise<AuthResponse> {
    // Firebase SDK会自动处理大部分回调，这里主要处理密码重置
    switch (mode) {
      case "resetPassword":
        return { success: true, redirectUrl: `/auth/reset-password?oobCode=${encodeURIComponent(oobCode)}` };
      default:
        return { success: true };
    }
  }

  async refreshToken(): Promise<boolean> {
    try {
      const { auth } = fireapp();
      if (auth.currentUser) {
        const idToken = await getIdToken(auth.currentUser, true); // 强制刷新
        const currentUser = get(userAuth);
        this._setUser({ ...currentUser, idToken });
        this.scheduleTokenRefresh();
        return true;
      }
      return false;
    } catch (error) {
      console.error("Token refresh failed:", error);
      this.signOut();
      return false;
    }
  }

  private scheduleTokenRefresh() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }
    
    this.refreshTimer = setTimeout(() => {
      this.refreshToken();
    }, this.TOKEN_REFRESH_INTERVAL);
  }

  getCurrentUser(): AuthUser | null {
    const user = get(userAuth);
    return user && user.idToken ? user : null;
  }

  async getUserInfo(_idToken?: string): Promise<any> {
    const { auth } = fireapp();
    const user = auth.currentUser;

    if (!user) {
      console.warn("用户未登录");
      return false;
    }

    return {
      localId: user.uid,
      email: user.email,
      emailVerified: user.emailVerified,
      displayName: user.displayName,
      photoUrl: user.photoURL,
      providerUserInfo: user.providerData
    };
  }

  isAuthenticated(): boolean {
    return !!this.getCurrentUser()?.idToken;
  }

  isEmailVerified(): boolean {
    return !!this.getCurrentUser()?.emailVerified;
  }

  isActivated(): boolean {
    return !!this.getCurrentUser()?.activated;
  }

  async activateUser(code: string): Promise<boolean> {
    try {
      const currentUser = this.getCurrentUser();
      if (!currentUser) {
        addToast({ type: "error", message: "请先登录" });
        return false;
      }

      const rateLimitKey = `activate_${currentUser.email}`;
      if (!authRateLimiter.isAllowed(rateLimitKey)) {
        const remainingTime = Math.ceil(authRateLimiter.getRemainingTime(rateLimitKey) / 1000 / 60);
        addToast({ type: "error", message: `激活尝试过于频繁，请${remainingTime}分钟后再试` });
        return false;
      }

      const cleanCode = InputValidator.sanitizeString(code.replace(/[^A-Za-z0-9]/g, '').toUpperCase(), 20);

      const response = await fetch("/api/user/activate", {
        method: "POST",
        headers: { "Content-Type": "application/json", "Authorization": `Bearer ${currentUser.idToken}` },
        body: JSON.stringify({ email: currentUser.email, activationCode: cleanCode }),
      });

      if (response.ok) {
        activationCode.set(cleanCode);
        authRateLimiter.reset(rateLimitKey);
        addToast({ type: "success", message: "激活成功！欢迎使用蘑菇AI小说平台" });
        return true;
      }
      const data = await response.json();
      throw new Error(data.error || "激活码验证失败");
    } catch (error: any) {
      addToast({ type: "error", message: error.message || "激活码验证失败" });
      return false;
    }
  }

  private _checkVerificationAndActivation(user: AuthUser): AuthResponse {
    if (!user.emailVerified) {
      return { success: true, user, redirectUrl: "/auth/verify-email" };
    }
    if (!user.activated) {
      return { success: true, user, redirectUrl: "/auth/activate" };
    }
    return { success: true, user };
  }

  private parseFirebaseError(error: any): AuthError {
    const errorCode = error?.code || error?.message || "UNKNOWN_ERROR";

    const errorMessages: { [key: string]: string } = {
      "auth/email-already-in-use": "该邮箱已被注册",
      "auth/operation-not-allowed": "该操作不被允许",
      "auth/too-many-requests": "尝试次数过多，请稍后再试",
      "auth/user-not-found": "邮箱不存在",
      "auth/wrong-password": "密码错误",
      "auth/user-disabled": "用户账号已被禁用",
      "auth/invalid-email": "邮箱格式无效",
      "auth/weak-password": "密码强度不够",
      "auth/missing-password": "请输入密码",
      "auth/invalid-credential": "登录凭据无效",
      "auth/credential-already-in-use": "该凭据已被其他账号使用",
      "auth/popup-closed-by-user": "登录窗口被用户关闭",
      "auth/popup-blocked": "登录弹窗被浏览器阻止",
      "auth/network-request-failed": "网络请求失败，请检查网络连接",
    };

    return {
      code: errorCode,
      message: errorMessages[errorCode] || `认证失败: ${errorCode}`
    };
  }

  // 清理资源
  destroy() {
    if (this.unsubscribeAuth) {
      this.unsubscribeAuth();
      this.unsubscribeAuth = null;
    }

    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }
}
