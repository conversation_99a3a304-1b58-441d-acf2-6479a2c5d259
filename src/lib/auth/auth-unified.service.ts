import { browser } from "$app/environment";
import { authStrategyFactory } from "./auth-strategy-factory.service";
import type { IAuthStrategy, AuthResponse, AuthUser, AuthStrategyConfig, AuthMethod } from "./auth-strategy.types";
import { AuthStrategy } from "./auth-strategy.types";

/**
 * 统一认证服务
 * 这个服务作为认证策略的统一入口，自动选择最佳的认证策略
 */
export class UnifiedAuthService implements IAuthStrategy {
  private factory = authStrategyFactory;

  readonly strategyType = AuthStrategy.HYBRID;

  // 获取当前使用的认证策略
  private getCurrentStrategy(): IAuthStrategy {
    return this.factory.getCurrentStrategy();
  }

  supportAuthMethod(method: AuthMethod): boolean {
    return this.getCurrentStrategy().supportAuthMethod(method);
  }

  async signUp(email: string, password: string): Promise<AuthResponse> {
    try {
      return await this.getCurrentStrategy().signUp(email, password);
    } catch (error) {
      console.warn('SignUp failed:', error);
      return { success: false, toast: { code: "STRATEGY_ERROR", message: "注册失败，请重试" } };
    }
  }

  async signIn(email: string, password: string): Promise<AuthResponse> {
    try {
      return await this.getCurrentStrategy().signIn(email, password);
    } catch (error) {
      console.warn('SignIn failed:', error);
      return { success: false, toast: { code: "STRATEGY_ERROR", message: "登录失败，请重试" } };
    }
  }

  async signInWithGoogle(): Promise<AuthResponse> {
    try {
      return await this.getCurrentStrategy().signInWithGoogle();
    } catch (error) {
      console.warn('Google SignIn failed:', error);
      return { success: false, toast: { code: "STRATEGY_ERROR", message: "登录失败，请重试" } };
    }
  }

  async signOut(): Promise<AuthResponse> {
    try {
      return await this.getCurrentStrategy().signOut();
    } catch (error) {
      console.warn('SignOut failed:', error);
      return { success: false, toast: { code: "STRATEGY_ERROR", message: "退出登录失败，请重试" } };
    }
  }

  async sendEmailVerification(idToken?: string): Promise<AuthResponse> {
    try {
      return await this.getCurrentStrategy().sendEmailVerification(idToken);
    } catch (error) {
      console.warn('Send email verification failed:', error);
      return { success: false, toast: { code: "STRATEGY_ERROR", message: "发送邮箱验证失败，请重试" } };
    }
  }

  async sendPasswordResetEmail(email: string): Promise<AuthResponse> {
    try {
      return await this.getCurrentStrategy().sendPasswordResetEmail(email);
    } catch (error) {
      console.warn('Send password reset email failed:', error);
      return { success: false, toast: { code: "STRATEGY_ERROR", message: "发送密码重置邮件失败，请重试" } };
    }
  }

  async sendEmailLinkSignIn(email: string): Promise<AuthResponse> {
    try {
      return await this.getCurrentStrategy().sendEmailLinkSignIn(email);
    } catch (error) {
      console.warn('Send email link failed:', error);
      return { success: false, toast: { code: "STRATEGY_ERROR", message: "发送邮箱链接失败，请重试" } };
    }
  }

  async handleAuthCallback(mode: string, oobCode: string): Promise<AuthResponse> {
    try {
      return await this.getCurrentStrategy().handleAuthCallback(mode, oobCode);
    } catch (error) {
      console.warn('Handle auth callback failed:', error);
      return { success: false, toast: { code: "STRATEGY_ERROR", message: "处理认证回调失败" } };
    }
  }

  async handlePasswordReset(oobCode: string, newPassword: string): Promise<AuthResponse> {
    try {
      return await this.getCurrentStrategy().handlePasswordReset(oobCode, newPassword);
    } catch (error) {
      console.warn('Handle password reset failed:', error);
      return { success: false, toast: { code: "STRATEGY_ERROR", message: "处理密码重置失败" } };
    }
  }

  async activateUser(code: string): Promise<AuthResponse> {
    try {
      return await this.getCurrentStrategy().activateUser(code);
    } catch (error) {
      console.warn('Activate user failed:', error);
      return { success: false, toast: { code: "STRATEGY_ERROR", message: "激活失败，请重试" } };
    }
  }

  async idToken(): Promise<string> {
    try {
      return await this.getCurrentStrategy().idToken();
    } catch (error) {
      console.warn('Get Id token failed:', error);
      return "";
    }
  }

  async reload(): Promise<AuthResponse> {
    try {
      return await this.getCurrentStrategy().reload();
    } catch (error) {
      console.warn('Reload user failed:', error);
      return { success: false, toast: { code: "STRATEGY_ERROR", message: "加载用户信息失败" } };
    }
  }

  getCurrentUser(): AuthUser | null {
    return this.getCurrentStrategy().getCurrentUser();
  }

  isAuthenticated(): boolean {
    try {
      return this.getCurrentStrategy().isAuthenticated();
    } catch (error) {
      console.warn('Check authentication failed:', error);
      return false;
    }
  }

  isEmailVerified(): boolean {
    try {
      return this.getCurrentStrategy().isEmailVerified();
    } catch (error) {
      console.warn('Check email verification failed:', error);
      return false;
    }
  }

  isActivated(): boolean {
    try {
      return this.getCurrentStrategy().isActivated();
    } catch (error) {
      console.warn('Check activation failed:', error);
      return false;
    }
  }

  async isAvailable(): Promise<boolean> {
    try {
      return this.getCurrentStrategy().isAvailable();
    } catch (error) {
      console.warn('Check service availability failed:', error);
      return false;
    }
  }

  async getHealthStatus(): Promise<{ healthy: boolean; latency?: number; }> {
    try {
      return this.getCurrentStrategy().getHealthStatus();
    } catch (error) {
      console.warn('Get health status failed:', error);
      return { healthy: false };
    }
  }

  // --- 策略管理 ---

  getCurrentStrategyType(): AuthStrategy {
    return this.factory.getCurrentStrategyType();
  }

  async switchStrategy(strategy: AuthStrategy): Promise<void> {
    await this.factory.switchStrategy(strategy);
  }

  async getStrategiesHealth() {
    return await this.factory.getStrategiesHealth();
  }

  updateConfig(config: Partial<AuthStrategyConfig>) {
    this.factory.updateConfig(config);
  }

  getConfig(): AuthStrategyConfig {
    return this.factory.getConfig();
  }

  async refreshAndRecommend(): Promise<AuthStrategy> {
    return await this.factory.refreshAndRecommend();
  }

  // 清理资源
  destroy() {
    this.factory.destroy();
  }
}

// 单例实例
export const unifiedAuthService = new UnifiedAuthService();

// 为了向后兼容，也导出为authService
export const authService = unifiedAuthService;
