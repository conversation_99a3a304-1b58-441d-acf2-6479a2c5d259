import { userAuth } from "$lib/stores/app.store";
import { get } from "svelte/store";
import { type IAuthStrategy, type AuthResponse, type AuthUser, type AuthToast, AuthMethod } from "./auth-strategy.types";
import { AuthStrategy } from "./auth-strategy.types";
import { InputValidator } from "$lib/utils/security";

// Firebase REST API 端点
const API_BASE_URL = "/api/googleapis?rest=";
const BASE_URL = "https://identitytoolkit.googleapis.com";
const SIGN_UP_URL = "https://identitytoolkit.googleapis.com/v1/accounts:signUp";
const SIGN_IN_PASSWORD_URL = "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword";
const SIGN_IN_EMAIL_LINK_URL = "https://identitytoolkit.googleapis.com/v1/accounts:signInWithEmailLink";
const SEND_OOB_CODE_URL = "https://identitytoolkit.googleapis.com/v1/accounts:sendOobCode";
const UPDATE_ACCOUNT_URL = "https://identitytoolkit.googleapis.com/v1/accounts:update";
const RESET_PASSWORD_URL = "https://identitytoolkit.googleapis.com/v1/accounts:resetPassword";
const REFRESH_TOKEN_URL = "https://securetoken.googleapis.com/v1/token";
const GET_ACCOUNT_INFO_URL = "https://identitytoolkit.googleapis.com/v1/accounts:lookup";

export class ServerProxyAuthStrategy implements IAuthStrategy {

  readonly strategyType = AuthStrategy.SERVER_PROXY;

  private _updateUser(firebaseUserData: any): AuthUser {
    userAuth.update((user: AuthUser) => {
      return {
        ...user,
        ...firebaseUserData,
      };
    });

    return get(userAuth);
  }

  supportAuthMethod(method: AuthMethod): boolean {
    return [
      AuthMethod.PASSWORD,
      AuthMethod.EMAIL_LINK,
    ].some(m => m === method);
  }

  async signUp(email: string, password: string): Promise<AuthResponse> {
    const emailValidation = InputValidator.validateEmail(email);
    if (!emailValidation.isValid) {
      return { success: false, toast: { code: "INVALID_EMAIL", message: emailValidation.error || "邮箱格式无效" } };
    }

    const passwordValidation = InputValidator.validatePassword(password);
    if (!passwordValidation.isValid) {
      return { success: false, toast: { code: "WEAK_PASSWORD", message: passwordValidation.errors.join("\n") || "密码强度不够" } };
    }

    try {
      const cleanEmail = InputValidator.sanitizeString(email.trim(), 254);

      const response = await fetch(`${API_BASE_URL}${SIGN_UP_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: cleanEmail, password, returnSecureToken: true }),
      });

      const data = await response.json();
      if (!response.ok) return { success: false, toast: this.parseFirebaseError(data.error) };

      const user = this._updateUser(data);
      await this.sendEmailVerification(user.idToken);

      return this._checkVerificationAndActivation(data);
    } catch (error) {
      return { success: false, toast: this.parseFirebaseError(error) };
    }
  }

  signIn(email: string, password: string): Promise<AuthResponse> {
    throw new Error("Method not implemented.");
  }

  async signInWithGoogle(): Promise<AuthResponse> {
    // 服务端代理策略不支持Google登录，需要切换到Firebase SDK策略
    return {
      success: false,
      toast: {
        code: "UNSUPPORTED_OPERATION",
        message: "服务端代理模式不支持Google登录，请切换到直连模式"
      }
    };
  }

  signOut(): Promise<AuthResponse> {
    throw new Error("Method not implemented.");
  }
  sendEmailVerification(idToken?: string): Promise<AuthResponse> {
    throw new Error("Method not implemented.");
  }
  sendPasswordResetEmail(email: string): Promise<AuthResponse> {
    throw new Error("Method not implemented.");
  }
  sendEmailLinkSignIn(email: string): Promise<AuthResponse> {
    throw new Error("Method not implemented.");
  }
  handleAuthCallback(mode: string, oobCode: string): Promise<AuthResponse> {
    throw new Error("Method not implemented.");
  }
  handlePasswordReset(oobCode: string, newPassword: string): Promise<AuthResponse> {
    throw new Error("Method not implemented.");
  }

  activateUser(code: string): Promise<AuthResponse> {
    throw new Error("Method not implemented.");
  }

  idToken(): Promise<string> {
    throw new Error("Method not implemented.");
  }

  reload(): Promise<AuthResponse> {
    throw new Error("Method not implemented.");
  }

  getCurrentUser(): AuthUser | null {
    const user = get(userAuth);
    return user && user.idToken ? user : null;
  }

  isAuthenticated(): boolean {
    return !!this.getCurrentUser()?.idToken;
  }
  isEmailVerified(): boolean {
    return !!this.getCurrentUser()?.emailVerified;
  }
  isActivated(): boolean {
    return !!this.getCurrentUser()?.activated;
  }

  async isAvailable(): Promise<boolean> {
    try {
      // 检查代理服务是否可用
      await fetch(`${API_BASE_URL}${BASE_URL}`);

      // 即使返回错误，只要能连接到代理服务就说明可用
      return true;
    } catch (error) {
      console.log('Server proxy not available:', error);
      return false;
    }
  }
  getHealthStatus(): Promise<{ healthy: boolean; latency?: number; }> {
    throw new Error("Method not implemented.");
  }

  private _checkVerificationAndActivation(user: AuthUser, continueUrl?: string): AuthResponse {
    if (!user.emailVerified) {
      return { success: true, user, redirectUrl: "/auth/verify-email" };
    }
    if (!user.activated) {
      return { success: true, user, redirectUrl: "/auth/activate" };
    }
    return { success: true, user, redirectUrl: continueUrl };
  }

  private parseFirebaseError(error: any): AuthToast {
    console.warn('auth-strategy-proxy.service parseFirebaseError:', error);

    const errorCode = error?.error?.message || error?.name || error?.message || "NETWORK_ERROR";
    const errorMessages: { [key: string]: string } = {
      "EMAIL_EXISTS": "该邮箱已被注册",
      "OPERATION_NOT_ALLOWED": "该操作不被允许",
      "TOO_MANY_ATTEMPTS_TRY_LATER": "尝试次数过多，请稍后再试",
      "EMAIL_NOT_FOUND": "邮箱不存在",
      "INVALID_PASSWORD": "密码错误",
      "USER_DISABLED": "用户账号已被禁用",
      "INVALID_EMAIL": "邮箱格式无效",
      "WEAK_PASSWORD": "密码强度不够",
      "MISSING_PASSWORD": "请输入密码",
      "INVALID_ID_TOKEN": "登录状态已过期，请重新登录",
      "USER_NOT_FOUND": "用户不存在",
      "CREDENTIAL_TOO_OLD_LOGIN_AGAIN": "登录状态已过期，请重新登录",
      "AbortError": "请求超时，请检查网络连接",
      "NETWORK_ERROR": "网络连接失败，请检查网络设置",
    };

    return {
      code: errorCode,
      message: errorMessages[errorCode] || `认证失败: ${errorCode}`
    };
  }
}
