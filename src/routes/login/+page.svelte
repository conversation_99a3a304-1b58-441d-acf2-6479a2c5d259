<script lang="ts">
  import { authService } from "$lib/services/auth.service";
  import { addToast } from "$lib/components/toast/toastStore";
  import AuthLayout from "$lib/components/auth/AuthLayout.svelte";
  import GoogleSignInButton from "$lib/components/auth/GoogleSignInButton.svelte";
  import { Input } from "$lib/components/ui";
  import { Mail, Lock, Eye, EyeOff } from "@lucide/svelte";
  import { AuthMethod } from "$lib/auth/auth-strategy.types";

  // 表单状态
  let email = $state("");
  let password = $state("");
  let showPassword = $state(false);
  let isLoading = $state(false);
  let emailError = $state("");
  let passwordError = $state("");

  // 登录模式：password | email-link
  let loginMode = $state<"password" | "email-link">("password");

  // 邮箱密码登录
  async function handlePasswordLogin() {
    isLoading = true;
    // AuthGuard 会订阅用户状态, 自动处理认证导航
    const result = await authService.signIn(email, password);

    if (!result.success) {
      switch (result.toast?.code) {
        case "INVALID_EMAIL":
          emailError = result.toast.message;
          break;
        case "WEAK_PASSWORD":
          passwordError = result.toast.message;
          break;
        default:
          addToast({
            type: "error",
            message: result.toast?.message || "登录失败",
          });
          break;
      }
    }
    isLoading = false;
  }

  // 邮箱链接登录
  async function handleEmailLinkLogin() {
    isLoading = true;
    const result = await authService.sendEmailLinkSignIn(email);
    if (!result.success) {
      switch (result.toast?.code) {
        case "INVALID_EMAIL":
          emailError = result.toast.message;
          break;
        default:
          addToast({
            type: "error",
            message: result.toast?.message || "登录失败",
          });
          break;
      }
    }
    isLoading = false;
  }

  // 处理表单提交
  function handleSubmit(event: Event) {
    event.preventDefault();
    if (loginMode === "password") {
      handlePasswordLogin();
    } else {
      handleEmailLinkLogin();
    }
  }

  // 切换密码显示
  function togglePasswordVisibility() {
    showPassword = !showPassword;
  }

  // 切换登录模式
  function switchLoginMode(mode: "password" | "email-link") {
    if (isLoading) return;
    loginMode = mode;
    passwordError = "";
    emailError = "";
  }
</script>

<svelte:head>
  <title>登录 - 蘑菇🍄 AI小说</title>
  <meta name="description" content="登录蘑菇AI小说创作平台，开启您的创作之旅" />
</svelte:head>

<AuthLayout
  title="欢迎回来"
  subtitle="登录您的账户，继续创作之旅"
  guard={{ requireAuth: false }}
>
  {#snippet children()}
    <!-- 登录模式切换 -->
    <div class="flex bg-gray-100 rounded-lg p-1 mb-6">
      <button
        onclick={() => switchLoginMode("password")}
        class="flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all duration-200 {loginMode ===
        'password'
          ? 'bg-white text-blue-600 shadow-sm'
          : 'text-gray-600 hover:text-gray-900'}"
        disabled={isLoading}
      >
        密码登录
      </button>
      <button
        onclick={() => switchLoginMode("email-link")}
        class="flex-1 py-2 px-4 text-sm font-medium rounded-md transition-all duration-200 {loginMode ===
        'email-link'
          ? 'bg-white text-blue-600 shadow-sm'
          : 'text-gray-600 hover:text-gray-900'}"
        disabled={isLoading}
      >
        邮箱链接
      </button>
    </div>

    <!-- 登录表单 -->
    <form onsubmit={handleSubmit} class="space-y-4">
      <!-- 邮箱输入 -->
      <div>
        <Input
          bind:value={email}
          type="email"
          label="邮箱地址"
          placeholder="请输入您的邮箱地址"
          error={emailError}
          required
          fullWidth
          autocomplete="email"
          disabled={isLoading}
        />
      </div>

      <!-- 密码输入 (仅密码登录模式) -->
      {#if loginMode === "password"}
        <div class="relative">
          <Input
            bind:value={password}
            type={showPassword ? "text" : "password"}
            label="密码"
            placeholder="请输入您的密码"
            error={passwordError}
            required
            fullWidth
            autocomplete="current-password"
            disabled={isLoading}
          />
          <button
            type="button"
            onclick={togglePasswordVisibility}
            class="absolute right-3 top-9 text-gray-400 hover:text-gray-600 transition-colors"
            disabled={isLoading}
          >
            {#if showPassword}
              <EyeOff class="w-5 h-5" />
            {:else}
              <Eye class="w-5 h-5" />
            {/if}
          </button>
        </div>
      {/if}

      <!-- 忘记密码链接 (仅密码登录模式) -->
      {#if loginMode === "password"}
        <div class="text-right">
          <a
            href="/auth/forgot-password"
            class="text-sm text-blue-600 hover:text-blue-700 transition-colors"
          >
            忘记密码？
          </a>
        </div>
      {/if}

      <!-- 提交按钮 -->
      <button
        type="submit"
        disabled={isLoading}
        class="w-full flex items-center justify-center px-4 py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-md hover:shadow-lg mt-6"
      >
        {#if isLoading}
          <svg
            class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          处理中...
        {:else if loginMode === "password"}
          <Lock class="w-4 h-4 mr-2" />
          登录
        {:else}
          <Mail class="w-4 h-4 mr-2" />
          发送登录链接
        {/if}
      </button>
    </form>

    <!-- 分隔线 -->
    <div class="relative my-6">
      <div class="absolute inset-0 flex items-center">
        <div class="w-full border-t border-gray-300"></div>
      </div>
      <div class="relative flex justify-center text-sm">
        <span class="px-2 bg-white text-gray-500">或</span>
      </div>
    </div>

    <!-- Google登录 -->
    <GoogleSignInButton
      hidden={authService.supportAuthMethod(AuthMethod.GOOGLE)}
    />

    <!-- 注册链接 -->
    <div class="text-center mt-6">
      <p class="text-sm text-gray-600">
        还没有账户？
        <a
          href="/auth/signup"
          class="text-blue-600 hover:text-blue-700 font-medium transition-colors"
        >
          立即注册
        </a>
      </p>
    </div>

    <!-- 邮箱链接登录说明 -->
    {#if loginMode === "email-link"}
      <div class="mt-4 p-3 bg-blue-50 rounded-lg">
        <p class="text-sm text-blue-800">
          <Mail class="w-4 h-4 inline mr-1" />
          我们将向您的邮箱发送一个安全的登录链接，点击即可登录。
        </p>
      </div>
    {/if}
  {/snippet}
</AuthLayout>
